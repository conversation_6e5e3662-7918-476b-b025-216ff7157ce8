# Log instrukcí a změn - PronajemExp

## Datum: 2024-12-19

### Požadavek uživatele:
Uživatel požádal o vytvoření dokumentace architektury aplikace a souboru pro sledování instrukcí.

### Provedené akce:

#### 1. Analýza aplikace
- Prozkoumána struktura aplikace pomocí codebase-retrieval
- Identifikovány hlavní komponenty a soubory
- Analyzovány dependencies v requirements.txt
- Prozkoumána struktura adresářů

#### 2. Vytvoření dokumentace architektury (ARCHITEKTURA.md)
**Obsah dokumentace:**
- P<PERSON>ehled aplikace - Discord bot pro monitoring pronájmů
- Technologie - Python, Discord.py, web scraping knihovny
- Struktura projektu - hlavní soubory a adresáře
- Architektura systému:
  - Discord Bot logika (main.py)
  - Scraper systém s abstraktní třídou ScraperBase
  - Konfigurace přes environment proměnné
  - Ukládání dat pomocí OffersStorage
  - Logování včetně Discord loggeru
- Workflow aplikace - od inicializace po odesílání nabídek
- Podporované portály (9 realitních portálů)
- Dispozice bytů (1+kk až 5++)
- Bezpečnost a limity
- Deployment informace

#### 3. Vytvoření logu instrukcí (INSTRUKCE_LOG.md)
- Soubor pro sledování všech požadavků a změn
- Strukturovaný formát s datem, požadavkem a provedenými akcemi
- Připraven pro budoucí rozšiřování

### Zjištěné informace o aplikaci:

**Účel:** Discord bot automaticky monitoruje 9 realitních portálů, vyhledává nové nabídky pronájmu bytů podle nakonfigurovaných kritérií a odesílá je do Discord kanálu.

**Klíčové funkce:**
- Periodické scrapování realitních portálů
- Filtrování podle ceny (max 14000 Kč)
- Detekce duplikátů pomocí hashování
- Odesílání jako Discord embedy
- Logování chyb do Discord kanálu

**Technická architektura:**
- Modulární design s abstraktní třídou pro scrapery
- Konfigurace přes environment proměnné
- Perzistentní ukládání hashů nabídek
- Asynchronní Discord bot s periodickými úlohami

**Stav aplikace:**
- Funkční aplikace s 9 implementovanými scrapery
- Obsahuje log soubory s historií běhu
- Má nakonfigurované environment proměnné
- Používá moderní Discord.py s app_commands

### Status: ✅ DOKONČENO
Vytvořena kompletní dokumentace architektury a inicializován log instrukcí.

---

## Datum: 2025-06-05

### Požadavek uživatele:
Uživatel hlásil, že scraper pro Bravis nefunguje správně a požádal o diagnostiku a opravu problému.

### Provedené akce:

#### 1. Diagnostika problému
- Vytvořen testovací skript `test_bravis_scraper.py` pro detailní analýzu
- Identifikován problém: HTTP požadavky fungují (status 200), ale CSS selektory nenacházejí elementy
- Uloženo HTML pro ruční analýzu (`bravis_debug.html`)
- Zjištěno: Webová stránka Bravis změnila HTML strukturu

#### 2. Analýza změn HTML struktury
**Původní (nefunkční) selektory:**
```python
for item in soup.select("#search > .in > .itemslist > li"):
    link = item.select_one("a.main").get("href")
    title = "Pronájem " + params[1].find("strong").get_text().strip() + ", " + params[2].find("strong").get_text().strip()
    location = item.select_one(".location").get_text().strip()
    price = item.select_one(".price")
    image_url = item.select_one(".img > img").get("src")
```

**Nová struktura HTML:**
- Kontejner nabídek: `.itemslist > .initemslist > .item` (místo `#search > .in > .itemslist > li`)
- Hlavní odkaz: `a` (první odkaz v `.item`, místo `a.main`)
- Název: `h1` uvnitř `.desc` (místo složité konstrukce z parametrů)
- Lokace: `.desc .location` (místo `.location`)
- Cena: `.desc .price` (místo `.price`)
- Obrázek: `.image img` (místo `.img > img`)

#### 3. Oprava scraperu
**Soubor:** `scrapers/scraper_bravis.py`
**Metoda:** `get_latest_offers()`

**Klíčové změny:**
- Aktualizovány CSS selektory podle nové HTML struktury
- Přidáno lepší error handling s try-catch bloky
- Přidáno logování varování při chybách parsování jednotlivých nabídek
- Zjednodušeno získávání názvu nabídky (přímo z `h1` místo konstrukce z parametrů)

#### 4. Testování opravy
**Výsledky testů:**
- ✅ **2+kk, 2+1**: 21 nabídek
- ✅ **3+kk, 3+1**: 14 nabídek
- ✅ **1+kk, 1+1**: 21 nabídek
- ✅ **Všechny dispozice**: 21 nabídek

**Příklady získaných nabídek:**
- "Pronájem bytu 2+1, Brno - Královo Pole, Božetěchova ulice, balkón, sklep, vhodné pro pár" - 18 300 Kč
- "Pronájem bytu 2+KK, 64 m², ul. Mlýnská, zařízený, balkon" - 20 000 Kč
- "Pronájem bytu 3+1, Brno - Nový Lískovec, Oblá ulice, balkón, komora a sklep" - 24 900 Kč

#### 5. Úklid
- Odstraněny testovací soubory (`test_bravis_scraper.py`, `test_bravis_final.py`, `bravis_debug.html`)

### Technické detaily opravy:

**Před opravou:** Scraper vracel 0 nabídek kvůli neplatným CSS selektorům
**Po opravě:** Scraper správně získává 14-21 nabídek podle dispozice

**Hlavní změny v kódu:**
```python
# Nové selektory
for item in soup.select(".itemslist .initemslist .item"):
    try:
        main_link = item.select_one("a")
        title = item.select_one(".desc h1").get_text().strip()
        location = item.select_one(".desc .location").get_text().strip()
        price_element = item.select_one(".desc .price")
        price_text = [text for text in price_element.stripped_strings][0]
        price = int(re.sub(r"[^\d]", "", price_text))
        image_url = urljoin(self.base_url, item.select_one(".image img").get("src"))

        # Vytvoření RentalOffer objektu...
    except Exception as e:
        logging.warning(f"Chyba při parsování nabídky z Bravis: {e}")
        continue
```

### Status: ✅ DOKONČENO
Bravis scraper byl úspěšně opraven a nyní funguje správně. Problém byl způsoben změnou HTML struktury na webu Bravis, která vyžadovala aktualizaci CSS selektorů.

---

## Datum: 2025-06-05 (druhá kontrola)

### Požadavek uživatele:
Uživatel požádal o kontrolu scraperu pro Euro Bydlení, podobně jako byla provedena oprava scraperu pro Bravis.

### Provedené akce:

#### 1. Diagnostika scraperu Euro Bydlení
- Vytvořen testovací skript `test_euro_bydleni_scraper.py` pro detailní analýzu
- Testovány různé dispozice: 2+kk/2+1, 3+kk/3+1, 1+kk/1+1, všechny dispozice
- **Dodatečný test s přesnými nastaveními z .env souboru** (`DISPOSITIONS=2+kk,2+1`)
- Všechny HTTP požadavky fungují správně (status 200)
- Uloženo HTML pro ruční analýzu

#### 2. Výsledky testování
**Všechny testy prošly úspěšně:**
- ✅ **2+kk, 2+1**: 12 nabídek
- ✅ **3+kk, 3+1**: 12 nabídek
- ✅ **1+kk, 1+1**: 12 nabídek
- ✅ **Všechny dispozice**: 12 nabídek
- ✅ **Test s uživatelovými nastaveními z .env** (`2+kk,2+1`): 12 nabídek

**Příklady získaných nabídek:**
- "Pronájem pěkného bytu 2+1, 50 m - Kuřim - ul. Na Loučkách 13" - 19 000 Kč
- "Pronájem bytu 2+kk, 54 m2, 2 lodžie, Brno, ul. Lipská" - 19 000 Kč
- "Pronájem bytu 1+1 mezi vilami na Černopolní, Brno" - 13 000 Kč

**Potvrzení funkčnosti s uživatelovými nastaveními:**
- Správně parsuje `DISPOSITIONS=2+kk,2+1` z .env souboru
- Nalezeny přesně očekávané dispozice: `2+1, 2+kk`
- Všechny nabídky odpovídají požadovaným kritériím (Brno, 2+kk/2+1, pronájem)

#### 3. Analýza HTML struktury
**Aktuální (funkční) selektory:**
```python
offers = soup.find(id="properties-box")
for item in offers.find_all("li", {"class": "list-items__item"}):
    title = content.find("h2", {"class": "list-items__item__title"})
    link = title.find("a").get('href')
    details = content.find_all("li")
    price = details[0].get_text()  # Cena
    location = details[1].get_text()  # Lokace
    image_url = image_container.find("img").get("src")
```

#### 4. Závěr
**Scraper Euro Bydlení funguje správně a nevyžaduje žádné opravy:**
- HTTP komunikace funguje bez problémů
- CSS selektory jsou aktuální a správné
- Parsování všech potřebných údajů funguje
- Získává správné informace: název, lokace, cena, odkaz, obrázek
- Počet 12 nabídek je standardní limit stránky

#### 5. Úklid
- Odstraněny testovací soubory (`test_euro_bydleni_scraper.py`, `test_euro_bydleni_with_env.py`, debug HTML soubory)

### Status: ✅ DOKONČENO
Scraper pro Euro Bydlení funguje správně a nevyžaduje žádné úpravy. Na rozdíl od Bravis scraperu, který vyžadoval opravu kvůli změně HTML struktury, Euro Bydlení scraper je plně funkční.

---

## Datum: 2025-06-05 (třetí kontrola)

### Požadavek uživatele:
Uživatel požádal o kontrolu scraperu pro Realcity, podobně jako byla provedena oprava scraperu pro Bravis a kontrola scraperu pro Euro Bydlení.

### Provedené akce:

#### 1. Diagnostika scraperu Realcity
- Vytvořen testovací skript `test_realcity_scraper.py` pro detailní analýzu
- Testovány různé dispozice: 2+kk/2+1, 3+kk/3+1, 1+kk/1+1, všechny dispozice
- **Dodatečný test s přesnými nastaveními z .env souboru** (`DISPOSITIONS=2+kk,2+1`)
- Všechny HTTP požadavky fungují správně (status 200)
- Uloženo HTML pro ruční analýzu

#### 2. Výsledky testování
**Všechny testy prošly úspěšně:**
- ✅ **2+kk, 2+1**: 8 nabídek
- ✅ **3+kk, 3+1**: 1 nabídka
- ✅ **1+kk, 1+1**: 10 nabídek
- ✅ **Všechny dispozice**: 10 nabídek
- ✅ **Test s uživatelovými nastaveními z .env** (`2+kk,2+1`): 8 nabídek

**Příklady získaných nabídek:**
- "Byt pronájem 2+kk 58 m²" - 17 500 Kč (Brno, Vymazalova)
- "Byt pronájem 2+kk 67 m²" - 21 000 Kč (Brno)
- "Byt pronájem 2+kk" - 16 000 Kč (Brno, Přímá)

**Potvrzení funkčnosti s uživatelovými nastaveními:**
- Správně parsuje `DISPOSITIONS=2+kk,2+1` z .env souboru
- Nalezeny přesně očekávané dispozice: `2+1, 2+kk`
- Všechny nabídky odpovídají požadovaným kritériím (Brno, 2+kk/2+1, pronájem)

#### 3. Analýza HTML struktury
**Aktuální (funkční) selektory:**
```python
for item in soup.select("#rc-advertise-result .media.advertise.item"):
    image = item.find("div", "pull-left image")
    body = item.find("div", "media-body")

    title = body.find("div", "title").a.get_text()
    link = body.find("div", "title").a.get("href")
    location = body.find("div", "address").get_text().strip()
    price = body.find("div", "price").get_text()
    image_url = image.img.get("src")
```

#### 4. Drobná oprava kódu
**Problém:** Duplicitní importy v souboru `scraper_realcity.py`
**Oprava:** Odstraněny duplicitní řádky:
```python
# Odstraněno:
from scrapers.rental_offer import RentalOffer  # duplicitní
import requests  # duplicitní
from bs4 import BeautifulSoup  # duplicitní
```

#### 5. Závěr
**Scraper Realcity funguje správně a nevyžaduje žádné zásadní opravy:**
- HTTP komunikace funguje bez problémů
- CSS selektory jsou aktuální a správné
- Parsování všech potřebných údajů funguje
- Získává správné informace: název, lokace, cena, odkaz, obrázek
- Počet 8-10 nabídek je realistický pro aktuální stav trhu
- Provedena pouze drobná úprava (odstranění duplicitních importů)

#### 6. Úklid
- Odstraněny testovací soubory (`test_realcity_scraper.py`, `test_realcity_final.py`, `realcity_debug.html`)

### Status: ✅ DOKONČENO
Scraper pro Realcity funguje správně a nevyžaduje žádné úpravy. Na rozdíl od Bravis scraperu, který vyžadoval opravu kvůli změně HTML struktury, Realcity scraper je plně funkční. Provedena pouze drobná úprava kódu (odstranění duplicitních importů).

---

## Šablona pro budoucí záznamy:

### Datum: YYYY-MM-DD
### Požadavek uživatele:
[Popis požadavku]

### Provedené akce:
[Seznam akcí]

### Status: [DOKONČENO/PROBÍHÁ/ČEKÁ]
[Poznámky]

---
